{
    ["rust_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        arch = "armeabi-v7a",
        plat = "android",
        __checked = true
    },
    ["tool_target_leviutils_android_armeabi-v7a_sh"] = {
        toolchain_info = {
            name = "ndk",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            plat = "android"
        },
        toolname = "clangxx",
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        arch = "armeabi-v7a",
        plat = "android",
        __checked = true
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        cross = "arm-linux-androideabi-",
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        plat = "android",
        __global = true,
        ndk_sdkver = "21",
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        ndkver = 25,
        arch = "armeabi-v7a",
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        __checked = true
    },
    ["tool_target_leviutils_android_armeabi-v7a_cxx"] = {
        toolchain_info = {
            name = "ndk",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            plat = "android"
        },
        toolname = "clangxx",
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    }
}