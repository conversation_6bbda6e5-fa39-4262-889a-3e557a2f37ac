{
    ["envs_arch_armeabi-v7a_plat_android"] = {
        arch = "armeabi-v7a",
        __checked = true,
        plat = "android",
        __global = true
    },
    ["tool_target_leviutils_android_armeabi-v7a_sh"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            arch = "armeabi-v7a",
            plat = "android",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            name = "ndk"
        }
    },
    ["tool_target_leviutils_android_armeabi-v7a_cxx"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            arch = "armeabi-v7a",
            plat = "android",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            name = "ndk"
        }
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        plat = "android",
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        ndkver = 25,
        __checked = true,
        cross = "arm-linux-androideabi-",
        arch = "armeabi-v7a",
        ndk_sdkver = "21",
        __global = true
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        arch = "armeabi-v7a",
        __checked = true,
        plat = "android",
        __global = true
    }
}