{
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            sdkver = "21",
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            cross = "arm-linux-androideabi-",
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            ndkver = 25
        }
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-mms-bitfields"] = true,
            ["-malign-double"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-mcrc"] = true,
            ["-mcmse"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-moutline-atomics"] = true,
            ["-fdeclspec"] = true,
            ["-fcall-saved-x14"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-F"] = true,
            ["-fminimize-whitespace"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-save-stats"] = true,
            ["-gline-tables-only"] = true,
            ["-g"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fapplication-extension"] = true,
            ["--analyzer-output"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-fno-fixed-point"] = true,
            ["-fcf-protection"] = true,
            ["-mfp32"] = true,
            ["-fcall-saved-x12"] = true,
            ["-dI"] = true,
            ["-miamcu"] = true,
            ["-fno-jump-tables"] = true,
            ["-I"] = true,
            ["-CC"] = true,
            ["-mno-local-sdata"] = true,
            ["-fmerge-all-constants"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-mlong-double-80"] = true,
            ["-ffixed-x20"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-ffixed-x19"] = true,
            ["-mstackrealign"] = true,
            ["-fdebug-types-section"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-maix-struct-return"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-pedantic"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-D"] = true,
            ["-fno-use-init-array"] = true,
            ["-print-target-triple"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-extract-api"] = true,
            ["-mno-implicit-float"] = true,
            ["-mpacked-stack"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-fno-xray-function-index"] = true,
            ["-help"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-G"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-Tbss"] = true,
            ["-mstack-arg-probe"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-imacros"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-mlocal-sdata"] = true,
            ["-mseses"] = true,
            ["-ffreestanding"] = true,
            ["-mrelax-all"] = true,
            ["-mno-restrict-it"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-Wdeprecated"] = true,
            ["-fno-signed-char"] = true,
            ["-gcodeview-ghash"] = true,
            ["-mexecute-only"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-include-pch"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-Tdata"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fdata-sections"] = true,
            ["-ffunction-sections"] = true,
            ["-fsanitize-trap"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-Xpreprocessor"] = true,
            ["-fno-temp-file"] = true,
            ["-fno-new-infallible"] = true,
            ["-fno-offload-lto"] = true,
            ["-fsplit-stack"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-fuse-line-directives"] = true,
            ["-z"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-mskip-rax-setup"] = true,
            ["-mmt"] = true,
            ["-fcoverage-mapping"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-mextern-sdata"] = true,
            ["-ffixed-a5"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-funroll-loops"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-fno-digraphs"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fno-rtti"] = true,
            ["-mrestrict-it"] = true,
            ["-gdwarf"] = true,
            ["-fdiscard-value-names"] = true,
            ["-fapprox-func"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-mpackets"] = true,
            ["-MMD"] = true,
            ["-ffixed-x11"] = true,
            ["-mwavefrontsize64"] = true,
            ["-mno-movt"] = true,
            ["-mrelax"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-fno-addrsig"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-mhvx-qfloat"] = true,
            ["-ffixed-x22"] = true,
            ["-fgpu-rdc"] = true,
            ["-ffixed-a3"] = true,
            ["-fno-split-stack"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-print-multiarch"] = true,
            ["-dM"] = true,
            ["-rpath"] = true,
            ["-emit-llvm"] = true,
            ["-fcall-saved-x15"] = true,
            ["-ffixed-x17"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-faligned-allocation"] = true,
            ["-meabi"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-femulated-tls"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-fno-operator-names"] = true,
            ["-mbackchain"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-mllvm"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-ffixed-d1"] = true,
            ["--analyze"] = true,
            ["-finline-functions"] = true,
            ["-Qy"] = true,
            ["-fno-common"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-fseh-exceptions"] = true,
            ["-fglobal-isel"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-mmadd4"] = true,
            ["-ffixed-x7"] = true,
            ["-fcommon"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-fsycl"] = true,
            ["-fcall-saved-x10"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-mfentry"] = true,
            ["-mnocrc"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-MF"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-emit-module"] = true,
            ["-Qn"] = true,
            ["-moutline"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-fembed-bitcode"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-ffixed-x29"] = true,
            ["-mqdsp6-compat"] = true,
            ["-mno-gpopt"] = true,
            ["-fapple-kext"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-mgpopt"] = true,
            ["-fmodules-ts"] = true,
            ["-emit-ast"] = true,
            ["-fno-stack-protector"] = true,
            ["-shared-libsan"] = true,
            ["-mno-global-merge"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-freg-struct-return"] = true,
            ["-pipe"] = true,
            ["-fgnu89-inline"] = true,
            ["-mno-seses"] = true,
            ["-fno-show-source-location"] = true,
            ["-ffixed-a2"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["--cuda-host-only"] = true,
            ["-mnop-mcount"] = true,
            ["-S"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-dD"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-fborland-extensions"] = true,
            ["-mno-neg-immediates"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-fwritable-strings"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-fno-unroll-loops"] = true,
            ["-fno-unique-section-names"] = true,
            ["-cl-finite-math-only"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-ffixed-d2"] = true,
            ["-cl-opt-disable"] = true,
            ["-dependency-file"] = true,
            ["-freciprocal-math"] = true,
            ["-fno-sycl"] = true,
            ["-mno-hvx"] = true,
            ["-mlong-double-64"] = true,
            ["-fcall-saved-x13"] = true,
            ["-mno-crc"] = true,
            ["-time"] = true,
            ["-mtgsplit"] = true,
            ["-mno-nvs"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-ffixed-x4"] = true,
            ["-ffixed-d7"] = true,
            ["-fcxx-exceptions"] = true,
            ["-fstack-protector"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-finstrument-functions"] = true,
            ["--help-hidden"] = true,
            ["-fms-compatibility"] = true,
            ["-pg"] = true,
            ["-Xanalyzer"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-fobjc-exceptions"] = true,
            ["-fno-elide-constructors"] = true,
            ["-Xclang"] = true,
            ["-nostdinc"] = true,
            ["-gdwarf-5"] = true,
            ["--migrate"] = true,
            ["-ffixed-a6"] = true,
            ["-mno-abicalls"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-index-header-map"] = true,
            ["-ftrapv"] = true,
            ["-emit-interface-stubs"] = true,
            ["-w"] = true,
            ["-iwithprefix"] = true,
            ["-fprofile-generate"] = true,
            ["-fvectorize"] = true,
            ["-fno-show-column"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-fdebug-macro"] = true,
            ["-fgnu-keywords"] = true,
            ["-mrtd"] = true,
            ["-arch"] = true,
            ["-fropi"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-save-temps"] = true,
            ["-isysroot"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-fnew-infallible"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-trigraphs"] = true,
            ["-fno-trigraphs"] = true,
            ["-ffixed-d4"] = true,
            ["-mno-mt"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-T"] = true,
            ["-gno-embed-source"] = true,
            ["-gdwarf-3"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-fenable-matrix"] = true,
            ["-fno-strict-return"] = true,
            ["-fwasm-exceptions"] = true,
            ["-mmsa"] = true,
            ["-ffixed-x26"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-mthread-model"] = true,
            ["-fstandalone-debug"] = true,
            ["-ffixed-x16"] = true,
            ["-ffixed-x25"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-U"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-ffixed-x12"] = true,
            ["-fno-rtti-data"] = true,
            ["-ffixed-a4"] = true,
            ["-rewrite-objc"] = true,
            ["-x"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-MP"] = true,
            ["-iwithprefixbefore"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-fxray-link-deps"] = true,
            ["-fpch-debuginfo"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-mnvj"] = true,
            ["-traditional-cpp"] = true,
            ["-ffixed-x9"] = true,
            ["-MD"] = true,
            ["-mmemops"] = true,
            ["-ffixed-x6"] = true,
            ["-fno-finite-loops"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-fno-builtin"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-ffast-math"] = true,
            ["-mlong-double-128"] = true,
            ["-fconvergent-functions"] = true,
            ["-mno-save-restore"] = true,
            ["-fno-lto"] = true,
            ["-fno-spell-checking"] = true,
            ["-mno-unaligned-access"] = true,
            ["-fno-declspec"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-ffixed-r19"] = true,
            ["-mno-memops"] = true,
            ["-iwithsysroot"] = true,
            ["-fdigraphs"] = true,
            ["-module-dependency-dir"] = true,
            ["-ffixed-x23"] = true,
            ["-fno-elide-type"] = true,
            ["-print-ivar-layout"] = true,
            ["-mcumode"] = true,
            ["-fno-pch-codegen"] = true,
            ["-module-file-info"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-mno-packets"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-mno-long-calls"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-fno-discard-value-names"] = true,
            ["-ffixed-x21"] = true,
            ["-msave-restore"] = true,
            ["-fprotect-parens"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-ffixed-x14"] = true,
            ["-fno-global-isel"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-ffinite-loops"] = true,
            ["-gcodeview"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["--gpu-bundle-output"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-include"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-MQ"] = true,
            ["-fsanitize-stats"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-ffixed-x1"] = true,
            ["-fforce-enable-int128"] = true,
            ["-fverbose-asm"] = true,
            ["-mno-execute-only"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fblocks"] = true,
            ["-ftime-trace"] = true,
            ["-print-effective-triple"] = true,
            ["-fsystem-module"] = true,
            ["-finline-hint-functions"] = true,
            ["-fstrict-enums"] = true,
            ["-fcall-saved-x8"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-fno-signed-zeros"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fno-standalone-debug"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-fexceptions"] = true,
            ["-ffixed-d0"] = true,
            ["-mglobal-merge"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-fms-hotpatch"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-fshort-wchar"] = true,
            ["-pthread"] = true,
            ["-fsave-optimization-record"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-ffixed-d3"] = true,
            ["-fno-memory-profile"] = true,
            ["-fpcc-struct-return"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fignore-exceptions"] = true,
            ["--emit-static-lib"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-isystem"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-serialize-diagnostics"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-fsigned-char"] = true,
            ["-static-libsan"] = true,
            ["-frwpi"] = true,
            ["-isystem-after"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-emit-merged-ifs"] = true,
            ["-Xlinker"] = true,
            ["-undef"] = true,
            ["-ivfsoverlay"] = true,
            ["-gdwarf64"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-ffixed-x3"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-cl-mad-enable"] = true,
            ["-o"] = true,
            ["-fasync-exceptions"] = true,
            ["-ffixed-x13"] = true,
            ["-mno-outline"] = true,
            ["-verify-pch"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-v"] = true,
            ["-mrecord-mcount"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-fstack-protector-all"] = true,
            ["-working-directory"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-munaligned-access"] = true,
            ["--config"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-gdwarf-2"] = true,
            ["-fmemory-profile"] = true,
            ["-ffixed-a1"] = true,
            ["-fcall-saved-x18"] = true,
            ["-foffload-lto"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-fmodules-decluse"] = true,
            ["-mfp64"] = true,
            ["-mnvs"] = true,
            ["--hip-link"] = true,
            ["-I-"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-mno-madd4"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-fcoroutines-ts"] = true,
            ["-mno-msa"] = true,
            ["-nogpuinc"] = true,
            ["-fxray-instrument"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-gline-directives-only"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-L"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-msoft-float"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-fstack-size-section"] = true,
            ["-fopenmp"] = true,
            ["-mno-code-object-v3"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-fjump-tables"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-fzvector"] = true,
            ["-fintegrated-as"] = true,
            ["-mno-tgsplit"] = true,
            ["-ibuiltininc"] = true,
            ["-mmark-bti-property"] = true,
            ["-fmodules-search-all"] = true,
            ["-fansi-escape-codes"] = true,
            ["-ffixed-x31"] = true,
            ["-ffixed-x2"] = true,
            ["-mno-embedded-data"] = true,
            ["-fno-debug-macro"] = true,
            ["-ffixed-x5"] = true,
            ["--no-cuda-version-check"] = true,
            ["-Qunused-arguments"] = true,
            ["-mno-cumode"] = true,
            ["-E"] = true,
            ["-fshort-enums"] = true,
            ["-ffixed-a0"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-ffixed-d5"] = true,
            ["-M"] = true,
            ["-fsized-deallocation"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fno-short-wchar"] = true,
            ["-Xassembler"] = true,
            ["-ffixed-x18"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fno-profile-generate"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-femit-all-decls"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-fno-plt"] = true,
            ["-cl-no-stdinc"] = true,
            ["-MJ"] = true,
            ["-fcall-saved-x11"] = true,
            ["-ffixed-x30"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-b"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-cxx-isystem"] = true,
            ["-fno-autolink"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-iquote"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-fpch-codegen"] = true,
            ["-fmath-errno"] = true,
            ["-gmodules"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fcxx-modules"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-mlong-calls"] = true,
            ["-fcs-profile-generate"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-fpascal-strings"] = true,
            ["-fobjc-weak"] = true,
            ["-B"] = true,
            ["-ffixed-x15"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-membedded-data"] = true,
            ["-ffixed-x8"] = true,
            ["-ffixed-d6"] = true,
            ["-ffixed-x10"] = true,
            ["-flto"] = true,
            ["-mabicalls"] = true,
            ["-c"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-dependency-dot"] = true,
            ["-ffixed-point"] = true,
            ["-dsym-dir"] = true,
            ["-fms-extensions"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-fslp-vectorize"] = true,
            ["-fgnu-runtime"] = true,
            ["-mhvx"] = true,
            ["--cuda-device-only"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-fobjc-arc"] = true,
            ["-MV"] = true,
            ["-fno-access-control"] = true,
            ["-MT"] = true,
            ["-fgpu-sanitize"] = true,
            ["-fintegrated-cc1"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-print-resource-dir"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-mlvi-cfi"] = true,
            ["-ffixed-r9"] = true,
            ["-ffixed-x28"] = true,
            ["--precompile"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-nobuiltininc"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["--version"] = true,
            ["-print-supported-cpus"] = true,
            ["-mibt-seal"] = true,
            ["-Xopenmp-target"] = true,
            ["-idirafter"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-Ttext"] = true,
            ["--verify-debug-info"] = true,
            ["-static-openmp"] = true,
            ["-gdwarf-4"] = true,
            ["-freroll-loops"] = true,
            ["-fmodules"] = true,
            ["-fstack-usage"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-P"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-relocatable-pch"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-gembed-source"] = true,
            ["-mno-relax"] = true,
            ["-print-targets"] = true,
            ["-fno-exceptions"] = true,
            ["-print-runtime-dir"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-mno-extern-sdata"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fcall-saved-x9"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-nohipwrapperinc"] = true,
            ["-nogpulib"] = true,
            ["-gdwarf32"] = true,
            ["-faddrsig"] = true,
            ["-fno-integrated-as"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-ftrigraphs"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-MM"] = true,
            ["-ffixed-x27"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-MG"] = true,
            ["-H"] = true,
            ["-msvr4-struct-return"] = true,
            ["-iprefix"] = true,
            ["-mcode-object-v3"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-mno-nvj"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-ffixed-x24"] = true,
            ["-print-search-dirs"] = true,
            ["-mno-outline-atomics"] = true,
            ["-fopenmp-simd"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-C"] = true,
            ["-fstack-clash-protection"] = true,
            ["-mlvi-hardening"] = true
        }
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    }
}