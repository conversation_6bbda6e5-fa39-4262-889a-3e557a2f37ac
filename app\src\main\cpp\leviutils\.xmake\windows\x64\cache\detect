{
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            sdkver = "21",
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            cross = "arm-linux-androideabi-",
            ndkver = 25,
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]]
        }
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-mcumode"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-ffixed-x9"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-fno-show-column"] = true,
            ["-fapprox-func"] = true,
            ["-mno-nvj"] = true,
            ["-fno-exceptions"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-mfp64"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-mno-neg-immediates"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-gline-directives-only"] = true,
            ["-fwritable-strings"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-fno-pch-codegen"] = true,
            ["-MQ"] = true,
            ["-fdigraphs"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-malign-double"] = true,
            ["-fno-short-wchar"] = true,
            ["-MP"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-fno-strict-return"] = true,
            ["-print-targets"] = true,
            ["-ffixed-x31"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-emit-interface-stubs"] = true,
            ["-save-temps"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-fno-elide-type"] = true,
            ["-fobjc-weak"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-fno-split-stack"] = true,
            ["-extract-api"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-fpascal-strings"] = true,
            ["-fobjc-arc"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-mno-gpopt"] = true,
            ["-fexceptions"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-mibt-seal"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-fstack-usage"] = true,
            ["-fmemory-profile"] = true,
            ["-mno-outline-atomics"] = true,
            ["-mno-mt"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-finline-hint-functions"] = true,
            ["-F"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-g"] = true,
            ["-mnocrc"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-fno-fixed-point"] = true,
            ["-fmodules-ts"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-ffunction-sections"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-gdwarf-4"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-Xopenmp-target"] = true,
            ["-mpacked-stack"] = true,
            ["-Tdata"] = true,
            ["-ffixed-x18"] = true,
            ["--analyzer-output"] = true,
            ["-membedded-data"] = true,
            ["-ffixed-x11"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-gline-tables-only"] = true,
            ["-fno-spell-checking"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-mmadd4"] = true,
            ["-fno-plt"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-ffixed-d0"] = true,
            ["-save-stats"] = true,
            ["-ffixed-d3"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-U"] = true,
            ["-dependency-dot"] = true,
            ["-gdwarf-2"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-mno-packets"] = true,
            ["-mlong-double-64"] = true,
            ["-mno-local-sdata"] = true,
            ["-MG"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-ffixed-a0"] = true,
            ["-G"] = true,
            ["-MF"] = true,
            ["-mno-crc"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-fcxx-modules"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-ffixed-d2"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-fno-use-init-array"] = true,
            ["--emit-static-lib"] = true,
            ["-fgnu-runtime"] = true,
            ["--cuda-host-only"] = true,
            ["-ffixed-x7"] = true,
            ["-emit-merged-ifs"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fopenmp-simd"] = true,
            ["-ffixed-x5"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-freciprocal-math"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-dependency-file"] = true,
            ["-print-search-dirs"] = true,
            ["-fprotect-parens"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-Tbss"] = true,
            ["-fgnu89-inline"] = true,
            ["-cl-no-stdinc"] = true,
            ["-faligned-allocation"] = true,
            ["-I"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-fcall-saved-x18"] = true,
            ["-MJ"] = true,
            ["-mno-execute-only"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-fsplit-stack"] = true,
            ["-mseses"] = true,
            ["-ffixed-x24"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-pedantic"] = true,
            ["-moutline-atomics"] = true,
            ["-meabi"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-mlvi-hardening"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-working-directory"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-dsym-dir"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-mcmse"] = true,
            ["-ffixed-point"] = true,
            ["-include"] = true,
            ["-mtgsplit"] = true,
            ["-ivfsoverlay"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-fcall-saved-x8"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-emit-llvm"] = true,
            ["-mno-nvs"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-verify-pch"] = true,
            ["-fapplication-extension"] = true,
            ["-fignore-exceptions"] = true,
            ["-ffixed-x14"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-fdebug-macro"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-MM"] = true,
            ["-fno-memory-profile"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-gmodules"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-mnvs"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-ffixed-x29"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-ffixed-x20"] = true,
            ["-ffixed-a6"] = true,
            ["-isysroot"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-ffixed-r9"] = true,
            ["-mrtd"] = true,
            ["-femulated-tls"] = true,
            ["-fstandalone-debug"] = true,
            ["-P"] = true,
            ["-ffreestanding"] = true,
            ["-fmodules-search-all"] = true,
            ["-mmark-bti-property"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["--cuda-device-only"] = true,
            ["-fgpu-sanitize"] = true,
            ["-miamcu"] = true,
            ["-fno-common"] = true,
            ["-mlong-double-80"] = true,
            ["-fnew-infallible"] = true,
            ["-M"] = true,
            ["-o"] = true,
            ["-MD"] = true,
            ["-fmath-errno"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-mno-extern-sdata"] = true,
            ["-Qy"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-mlong-double-128"] = true,
            ["-nogpulib"] = true,
            ["-isystem"] = true,
            ["-dM"] = true,
            ["--gpu-bundle-output"] = true,
            ["-fmodules-decluse"] = true,
            ["-mhvx-qfloat"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-mno-madd4"] = true,
            ["-fmodules"] = true,
            ["-fno-elide-constructors"] = true,
            ["-mrelax-all"] = true,
            ["-mno-abicalls"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-traditional-cpp"] = true,
            ["-fverbose-asm"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-print-effective-triple"] = true,
            ["-funroll-loops"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-foffload-lto"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-ffixed-x10"] = true,
            ["-fno-global-isel"] = true,
            ["-B"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-time"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-gcodeview-ghash"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["--precompile"] = true,
            ["-ffixed-d6"] = true,
            ["-fcommon"] = true,
            ["-mextern-sdata"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-fwasm-exceptions"] = true,
            ["-fcall-saved-x13"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-fcxx-exceptions"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["--analyze"] = true,
            ["-freroll-loops"] = true,
            ["-mno-long-calls"] = true,
            ["-mno-code-object-v3"] = true,
            ["-iquote"] = true,
            ["-mno-seses"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-mpackets"] = true,
            ["-isystem-after"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-femit-all-decls"] = true,
            ["-ffixed-x12"] = true,
            ["-z"] = true,
            ["--help-hidden"] = true,
            ["-mno-cumode"] = true,
            ["-pg"] = true,
            ["-mstackrealign"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-mno-save-restore"] = true,
            ["-fstack-size-section"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-ftrapv"] = true,
            ["-fglobal-isel"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-rewrite-objc"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-fdebug-types-section"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-fstack-clash-protection"] = true,
            ["-ffixed-x17"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-fno-unique-section-names"] = true,
            ["-mnvj"] = true,
            ["-fansi-escape-codes"] = true,
            ["-fsanitize-trap"] = true,
            ["-static-libsan"] = true,
            ["-dD"] = true,
            ["-serialize-diagnostics"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fsized-deallocation"] = true,
            ["-x"] = true,
            ["-fgpu-rdc"] = true,
            ["-ffixed-x26"] = true,
            ["-mno-hvx"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-fembed-bitcode"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-nostdinc"] = true,
            ["-ffixed-x22"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-cxx-isystem"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-msoft-float"] = true,
            ["-E"] = true,
            ["-w"] = true,
            ["-mfp32"] = true,
            ["-mbackchain"] = true,
            ["-fcall-saved-x15"] = true,
            ["-mrecord-mcount"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-ffixed-r19"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-gdwarf64"] = true,
            ["--no-cuda-version-check"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-fblocks"] = true,
            ["-fasync-exceptions"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-msave-restore"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-mnop-mcount"] = true,
            ["-ffinite-loops"] = true,
            ["-ibuiltininc"] = true,
            ["-fsycl"] = true,
            ["-mstack-arg-probe"] = true,
            ["-fstrict-enums"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-nogpuinc"] = true,
            ["-fvectorize"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-ffixed-x25"] = true,
            ["-print-runtime-dir"] = true,
            ["-fcall-saved-x14"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-MV"] = true,
            ["-fropi"] = true,
            ["-fno-sycl"] = true,
            ["-fslp-vectorize"] = true,
            ["-mmsa"] = true,
            ["-fno-show-source-location"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-fno-addrsig"] = true,
            ["-shared-libsan"] = true,
            ["--hip-link"] = true,
            ["-ffixed-x1"] = true,
            ["-fno-operator-names"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-Xanalyzer"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-Xclang"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-print-multiarch"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fms-compatibility"] = true,
            ["-v"] = true,
            ["-fsanitize-stats"] = true,
            ["-fno-digraphs"] = true,
            ["-mcode-object-v3"] = true,
            ["-gembed-source"] = true,
            ["-ffixed-x15"] = true,
            ["-C"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-mglobal-merge"] = true,
            ["-fshort-enums"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-moutline"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-fno-debug-macro"] = true,
            ["--version"] = true,
            ["-ffixed-x28"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-fxray-instrument"] = true,
            ["-H"] = true,
            ["-undef"] = true,
            ["-fjump-tables"] = true,
            ["-flto"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-mwavefrontsize64"] = true,
            ["-fopenmp"] = true,
            ["-fpch-codegen"] = true,
            ["-ffixed-x21"] = true,
            ["-ffixed-d4"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-fconvergent-functions"] = true,
            ["-finstrument-functions"] = true,
            ["-mlvi-cfi"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-mthread-model"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-mno-relax"] = true,
            ["-mno-implicit-float"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-L"] = true,
            ["-relocatable-pch"] = true,
            ["-ffixed-x19"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-fprofile-generate"] = true,
            ["-print-resource-dir"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-munaligned-access"] = true,
            ["-fzvector"] = true,
            ["-module-dependency-dir"] = true,
            ["-c"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-ffixed-a4"] = true,
            ["-fborland-extensions"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-ffixed-x27"] = true,
            ["-ffixed-x4"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-ffixed-x8"] = true,
            ["-mskip-rax-setup"] = true,
            ["-mno-global-merge"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-gcodeview"] = true,
            ["-fcall-saved-x9"] = true,
            ["-menable-experimental-extensions"] = true,
            ["--verify-debug-info"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-ffixed-d7"] = true,
            ["-ftime-trace"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fcall-saved-x11"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-ffixed-a2"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-mno-outline"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-mrelax"] = true,
            ["--migrate"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-Qn"] = true,
            ["-fno-rtti-data"] = true,
            ["-fms-hotpatch"] = true,
            ["-fstack-protector"] = true,
            ["-module-file-info"] = true,
            ["-fno-integrated-as"] = true,
            ["-fno-rtti"] = true,
            ["-iprefix"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-fmerge-all-constants"] = true,
            ["-mgpopt"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-help"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-ffast-math"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-ffixed-x13"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-ffixed-d1"] = true,
            ["-mno-memops"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-ftrigraphs"] = true,
            ["-fno-profile-generate"] = true,
            ["-mcrc"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-gdwarf-3"] = true,
            ["-fno-signed-char"] = true,
            ["-mfentry"] = true,
            ["-fapple-kext"] = true,
            ["-mmt"] = true,
            ["-freg-struct-return"] = true,
            ["-fcall-saved-x12"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-index-header-map"] = true,
            ["-static-openmp"] = true,
            ["-iwithprefixbefore"] = true,
            ["-ffixed-a1"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-gdwarf-5"] = true,
            ["-fcs-profile-generate"] = true,
            ["-gdwarf"] = true,
            ["-mno-msa"] = true,
            ["-mqdsp6-compat"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-print-ivar-layout"] = true,
            ["-S"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-frwpi"] = true,
            ["-mabicalls"] = true,
            ["-imacros"] = true,
            ["-mrestrict-it"] = true,
            ["-ffixed-x6"] = true,
            ["-Ttext"] = true,
            ["-fno-standalone-debug"] = true,
            ["-mms-bitfields"] = true,
            ["-fcf-protection"] = true,
            ["-ffixed-x30"] = true,
            ["-b"] = true,
            ["-ffixed-a3"] = true,
            ["-gdwarf32"] = true,
            ["-fno-new-infallible"] = true,
            ["-iwithsysroot"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-cl-finite-math-only"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-fsigned-char"] = true,
            ["-fstack-protector-all"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-Xpreprocessor"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-fuse-line-directives"] = true,
            ["-fno-builtin"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-nobuiltininc"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-Xassembler"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-fcoroutines-ts"] = true,
            ["-fno-declspec"] = true,
            ["-Xlinker"] = true,
            ["-fshort-wchar"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-fno-offload-lto"] = true,
            ["-nohipwrapperinc"] = true,
            ["-mno-movt"] = true,
            ["-emit-module"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-dI"] = true,
            ["-ffixed-a5"] = true,
            ["-idirafter"] = true,
            ["-CC"] = true,
            ["-include-pch"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-fminimize-whitespace"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-mllvm"] = true,
            ["-msvr4-struct-return"] = true,
            ["-fpcc-struct-return"] = true,
            ["-fenable-matrix"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-pipe"] = true,
            ["-cl-mad-enable"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-fno-finite-loops"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-mmemops"] = true,
            ["--config"] = true,
            ["-trigraphs"] = true,
            ["-fsave-optimization-record"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-fno-stack-protector"] = true,
            ["-fno-unroll-loops"] = true,
            ["-mno-embedded-data"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-Qunused-arguments"] = true,
            ["-emit-ast"] = true,
            ["-fxray-link-deps"] = true,
            ["-fms-extensions"] = true,
            ["-mlong-calls"] = true,
            ["-fpch-debuginfo"] = true,
            ["-ffixed-x2"] = true,
            ["-ffixed-x16"] = true,
            ["-fforce-enable-int128"] = true,
            ["-fdiscard-value-names"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-fno-temp-file"] = true,
            ["-fobjc-exceptions"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-fcoverage-mapping"] = true,
            ["-ffixed-x23"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-MMD"] = true,
            ["-arch"] = true,
            ["-mno-tgsplit"] = true,
            ["-fno-access-control"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-print-supported-cpus"] = true,
            ["-iwithprefix"] = true,
            ["-fintegrated-as"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-fno-jump-tables"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-mno-unaligned-access"] = true,
            ["-D"] = true,
            ["-mhvx"] = true,
            ["-gno-embed-source"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-T"] = true,
            ["-print-target-triple"] = true,
            ["-fno-signed-zeros"] = true,
            ["-MT"] = true,
            ["-fno-lto"] = true,
            ["-fsystem-module"] = true,
            ["-fintegrated-cc1"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-fgnu-keywords"] = true,
            ["-fno-discard-value-names"] = true,
            ["-fno-trigraphs"] = true,
            ["-mno-restrict-it"] = true,
            ["-mexecute-only"] = true,
            ["-pthread"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-ffixed-d5"] = true,
            ["-finline-functions"] = true,
            ["-fno-autolink"] = true,
            ["-cl-opt-disable"] = true,
            ["-mlocal-sdata"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-faddrsig"] = true,
            ["-Wdeprecated"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-ffixed-x3"] = true,
            ["-fseh-exceptions"] = true,
            ["-rpath"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fdeclspec"] = true,
            ["-fdata-sections"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-I-"] = true,
            ["-maix-struct-return"] = true
        }
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--high-entropy-va"] = true,
            ["--exclude-all-symbols"] = true,
            ["-v"] = true,
            ["--disable-auto-import"] = true,
            ["--no-fatal-warnings"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--Bstatic"] = true,
            ["--large-address-aware"] = true,
            ["--disable-tsaware"] = true,
            ["--nxcompat"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["-m"] = true,
            ["--strip-debug"] = true,
            ["--Bdynamic"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--no-insert-timestamp"] = true,
            ["--verbose"] = true,
            ["--gc-sections"] = true,
            ["--export-all-symbols"] = true,
            ["--appcontainer"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--fatal-warnings"] = true,
            ["--disable-no-seh"] = true,
            ["--whole-archive"] = true,
            ["-s"] = true,
            ["--allow-multiple-definition"] = true,
            ["-dn"] = true,
            ["--no-dynamicbase"] = true,
            ["--strip-all"] = true,
            ["-L"] = true,
            ["--dynamicbase"] = true,
            ["-l"] = true,
            ["--tsaware"] = true,
            ["-o"] = true,
            ["--no-demangle"] = true,
            ["--help"] = true,
            ["--enable-auto-import"] = true,
            ["-S"] = true,
            ["--version"] = true,
            ["--disable-dynamicbase"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--disable-nxcompat"] = true,
            ["--shared"] = true,
            ["--demangle"] = true,
            ["--insert-timestamp"] = true,
            ["-dy"] = true,
            ["-static"] = true,
            ["--no-seh"] = true,
            ["--no-whole-archive"] = true,
            ["--kill-at"] = true,
            ["--no-gc-sections"] = true
        }
    }
}