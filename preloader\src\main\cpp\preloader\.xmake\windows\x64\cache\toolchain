{
    zig_arch_x64_plat_windows = {
        __checked = false,
        plat = "windows",
        arch = "x64"
    },
    gfortran_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        arch = "x64"
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        plat = "android",
        __checked = true,
        arch = "armeabi-v7a"
    },
    go_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        arch = "x64"
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        cross = "arm-linux-androideabi-",
        arch = "armeabi-v7a",
        ndk_sdkver = "21",
        ndkver = 25,
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        __global = true,
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        __checked = true
    },
    fpc_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        arch = "x64"
    },
    clang_arch_x64_plat_windows = {
        __checked = false,
        plat = "windows",
        arch = "x64"
    },
    nasm_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        arch = "x64"
    },
    msvc_arch_x64_plat_windows = {
        __checked = false,
        plat = "windows",
        arch = "x64"
    },
    ["tool_target_preloader_android_armeabi-v7a_cxx"] = {
        toolname = "clangxx",
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            arch = "armeabi-v7a",
            name = "ndk",
            plat = "android",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        }
    },
    swift_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        arch = "x64"
    },
    rust_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        arch = "x64"
    },
    ["tool_target_preloader_android_armeabi-v7a_sh"] = {
        toolname = "clangxx",
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            arch = "armeabi-v7a",
            name = "ndk",
            plat = "android",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        }
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        plat = "android",
        __checked = true,
        arch = "armeabi-v7a"
    },
    yasm_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        arch = "x64"
    },
    cuda_arch_x64_plat_windows = {
        __checked = true,
        plat = "windows",
        arch = "x64"
    },
    nim_arch_x64_plat_windows = {
        __checked = false,
        plat = "windows",
        arch = "x64"
    }
}