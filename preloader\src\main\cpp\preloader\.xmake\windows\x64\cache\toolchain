{
    swift_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    clang_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = false
    },
    msvc_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = false
    },
    go_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    yasm_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    ["tool_target_preloader_android_armeabi-v7a_cxx"] = {
        toolname = "clangxx",
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            name = "ndk",
            arch = "armeabi-v7a",
            plat = "android",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        }
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        arch = "armeabi-v7a",
        __global = true,
        __checked = true
    },
    rust_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    nasm_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    gfortran_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    cuda_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    zig_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = false
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        __global = true,
        arch = "armeabi-v7a",
        ndkver = 25,
        cross = "arm-linux-androideabi-",
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        ndk_sdkver = "21",
        __checked = true,
        plat = "android",
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]]
    },
    nim_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = false
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        arch = "armeabi-v7a",
        __global = true,
        __checked = true
    },
    fpc_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = true
    },
    ["tool_target_preloader_android_armeabi-v7a_sh"] = {
        toolname = "clangxx",
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            name = "ndk",
            arch = "armeabi-v7a",
            plat = "android",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        }
    }
}