2025-07-31 00:44:37.039 27660-27757 ProfileInstaller        org.levimc.launcher                  D  Installing profile for org.levimc.launcher
2025-07-31 00:44:40.117 27660-27660 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-31 00:44:40.122 27660-27660 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=high hint, reason=touch, vri=VRI[MainActivity]@2f3909d
2025-07-31 00:44:40.278 27660-27660 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-31 00:44:40.293 27660-27764 LeviLogger              org.levimc.launcher                  I  [LeviMC] Cleaning cache directory...
2025-07-31 00:44:40.294 27660-27764 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/BaseDexClassLoader;->pathList:Ldalvik/system/DexPathList; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:44:40.294 27660-27764 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->addDexPath(Ljava/lang/String;Ljava/io/File;)V (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:44:40.295 27660-27764 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: launcher.dex
2025-07-31 00:44:40.296 27660-27764 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: launcher.dex
2025-07-31 00:44:40.306 27660-27660 Dialog                  org.levimc.launcher                  I  mIsDeviceDefault = false, mIsSamsungBasicInteraction = false, isMetaDataInActivity = false
2025-07-31 00:44:40.343 27660-27660 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff010102 d=android.graphics.drawable.InsetDrawable@3c1755e
2025-07-31 00:44:40.344 27660-27660 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=0 d=android.graphics.drawable.ColorDrawable@fe32b55
2025-07-31 00:44:40.344 27660-27660 WindowManager           org.levimc.launcher                  I  WindowManagerGlobal#addView, ty=2, view=com.android.internal.policy.DecorView{3ab896a V.ED..... R.....I. 0,0-0,0}[MainActivity], caller=android.view.WindowManagerImpl.addView:158 android.app.Dialog.show:511 org.levimc.launcher.core.minecraft.MinecraftLauncher.showLoading:307 
2025-07-31 00:44:40.346 27660-27660 ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-31 00:44:40.347 27660-27680 NativeCust...ncyManager org.levimc.launcher                  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-31 00:44:40.357 27660-27660 InputTransport          org.levimc.launcher                  D  Input channel constructed: '3b5b1cf', fd=149
2025-07-31 00:44:40.359 27660-27660 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-31 00:44:40.359 27660-27660 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-31 00:44:40.359 27660-27660 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@3ab896a IsHRR=false TM=true
2025-07-31 00:44:40.377 27660-27660 BufferQueueConsumer     org.levimc.launcher                  D  [](id:6c0c00000002,api:0,p:-1,c:27660) connect: controlledByApp=false
2025-07-31 00:44:40.377 27660-27660 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@5e3fe5b#2](f:0,a:0,s:0) constructor()
2025-07-31 00:44:40.377 27660-27660 BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[MainActivity]@5e3fe5b mNativeObject= 0xb4000079e58a8c00 sc.mNativeObject= 0xb400007988328bc0 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-31 00:44:40.378 27660-27660 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 556 h= 590 mName = VRI[MainActivity]@5e3fe5b mNativeObject= 0xb4000079e58a8c00 sc.mNativeObject= 0xb400007988328bc0 format= -2 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-31 00:44:40.378 27660-27660 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@5e3fe5b#2](f:0,a:0,s:0) update width=556 height=590 format=-2 mTransformHint=4
2025-07-31 00:44:40.378 27660-27660 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(982,335,1358,745) relayoutAsync=false req=(376,410)0 dur=10 res=0x3 s={true 0xb400007989145000} ch=true seqId=0
2025-07-31 00:44:40.379 27660-27660 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-31 00:44:40.379 27660-27660 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007989145000} hwInitialized=true
2025-07-31 00:44:40.380 27660-27660 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-31 00:44:40.380 27660-27660 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[MainActivity]@5e3fe5b#6
2025-07-31 00:44:40.380 27660-27660 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  Creating new active sync group VRI[MainActivity]@5e3fe5b#7
2025-07-31 00:44:40.380 27660-27660 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-31 00:44:40.382 27660-27714 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-31 00:44:40.382 27660-27714 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  mWNT: t=0xb40000798819f080 mBlastBufferQueue=0xb4000079e58a8c00 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-31 00:44:40.382 27660-27714 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-31 00:44:40.385 27660-27764 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes2.dex
2025-07-31 00:44:40.389 27660-27680 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@5e3fe5b#2](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-31 00:44:40.389 27660-27680 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@5e3fe5b#2](f:0,a:1,s:0) acquireNextBufferLocked size=556x590 mFrameNumber=1 applyTransaction=true mTimestamp=314324370998119(auto) mPendingTransactions.size=0 graphicBufferId=118798795407386 transform=7
2025-07-31 00:44:40.389 27660-27680 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-31 00:44:40.391 27660-27680 HWUI                    org.levimc.launcher                  D  CFMS:: SetUp Pid : 27660    Tid : 27680
2025-07-31 00:44:40.391 27660-27660 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-31 00:44:40.400 27660-27680 HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-31 00:44:40.407 27660-27660 ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-31 00:44:40.408 27660-27660 ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-31 00:44:40.441 27660-27764 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes2.dex
2025-07-31 00:44:40.442 27660-27660 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb400007989145000}
2025-07-31 00:44:40.442 27660-27660 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-31 00:44:40.442 27660-27660 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-31 00:44:40.446 27660-27669 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=160
2025-07-31 00:44:40.455 27660-27660 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity
2025-07-31 00:44:40.501 27660-27764 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes.dex
2025-07-31 00:44:40.550 27660-27764 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes.dex
2025-07-31 00:44:40.551 27660-27764 LeviLogger              org.levimc.launcher                  I  [LeviMC] /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64
2025-07-31 00:44:40.551 27660-27764 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->nativeLibraryDirectories:Ljava/util/List; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:44:40.551 27660-27764 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->nativeLibraryPathElements:[Ldalvik/system/DexPathList$NativeLibraryElement; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:44:40.551 27660-27764 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->makePathElements(Ljava/util/List;)[Ldalvik/system/DexPathList$NativeLibraryElement; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:44:40.551 27660-27764 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->systemNativeLibraryDirectories:Ljava/util/List; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-31 00:44:40.558 27660-27770 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libc++_shared.so using class loader ns clns-7 (caller=/data/app/~~4iO4h2EBOJs_nIve8Gwjrw==/org.levimc.launcher-oElbwfP_8qlcFOlnWt_2pw==/base.apk!classes7.dex): ok
2025-07-31 00:44:40.560 27660-27770 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libfmod.so using class loader ns clns-7 (caller=/data/app/~~4iO4h2EBOJs_nIve8Gwjrw==/org.levimc.launcher-oElbwfP_8qlcFOlnWt_2pw==/base.apk!classes7.dex): ok
2025-07-31 00:44:40.668 27660-27770 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so using class loader ns clns-7 (caller=/data/app/~~4iO4h2EBOJs_nIve8Gwjrw==/org.levimc.launcher-oElbwfP_8qlcFOlnWt_2pw==/base.apk!classes7.dex): ok
2025-07-31 00:44:40.668 27660-27770 Minecraft               org.levimc.launcher                  V  Entering JNI_OnLoad 0x78d8ab35f0
2025-07-31 00:44:40.668 27660-27770 Minecraft               org.levimc.launcher                  V  JNI_OnLoad completed
2025-07-31 00:44:40.716 27660-27660 ActivityThread          org.levimc.launcher                  D  org.levimc.launcher will use render engine as VK
2025-07-31 00:44:40.721 27660-27660 GlossHook               org.levimc.launcher                  I  GlossHook v1.9.2, Created by XMDS.
2025-07-31 00:44:40.721 27660-27660 GlossHook               org.levimc.launcher                  I  GlossHook is exist at: /data/app/~~4iO4h2EBOJs_nIve8Gwjrw==/org.levimc.launcher-oElbwfP_8qlcFOlnWt_2pw==/base.apk!/lib/arm64-v8a/libpreloader.so
2025-07-31 00:44:40.721 27660-27660 GlossHook               org.levimc.launcher                  I  Gloss Elfinfo init...
2025-07-31 00:44:40.721 27660-27660 nativeloader            org.levimc.launcher                  D  Load /data/app/~~4iO4h2EBOJs_nIve8Gwjrw==/org.levimc.launcher-oElbwfP_8qlcFOlnWt_2pw==/base.apk!/lib/arm64-v8a/libpreloader.so using class loader ns clns-7 (caller=/data/user/0/org.levimc.launcher/code_cache/dex/launcher.dex): ok
2025-07-31 00:44:40.730 27660-27665 levimc.launcher         org.levimc.launcher                  W  Cleared Reference was only reachable from finalizer (only reported once)
2025-07-31 00:44:40.736 27660-27660 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Landroid/content/res/AssetManager;->addAssetPath(Ljava/lang/String;)I (runtime_flags=0, domain=platform, api=unsupported) from Lcom/mojang/minecraftpe/Launcher; (domain=app) using reflection: allowed
2025-07-31 00:44:40.768 27660-27660 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff303030 d=android.graphics.drawable.ColorDrawable@ffb3a6c
2025-07-31 00:44:40.772 27660-27666 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'ClientS', fd=232
2025-07-31 00:44:40.772 27660-27666 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'ClientS', fd=160
2025-07-31 00:44:40.778 27660-27660 nativeloader            org.levimc.launcher                  D  Load /data/app/~~4iO4h2EBOJs_nIve8Gwjrw==/org.levimc.launcher-oElbwfP_8qlcFOlnWt_2pw==/base.apk!/lib/arm64-v8a/libpreloader.so using class loader ns clns-7 (caller=<unknown>): ok
2025-07-31 00:44:40.780 27660-27667 System                  org.levimc.launcher                  W  A resource failed to call ZipFile.close. 
2025-07-31 00:44:40.782 27660-27660 ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-31 00:44:40.784 27660-27667 levimc.launcher         org.levimc.launcher                  W  ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/base.apk' with 1 weak references
2025-07-31 00:44:40.784 27660-27667 levimc.launcher         org.levimc.launcher                  W  ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.arm64_v8a.apk' with 1 weak references
2025-07-31 00:44:40.784 27660-27667 levimc.launcher         org.levimc.launcher                  W  ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.bn.apk' with 1 weak references
2025-07-31 00:44:40.784 27660-27667 levimc.launcher         org.levimc.launcher                  W  ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.en.apk' with 1 weak references
2025-07-31 00:44:40.784 27660-27667 levimc.launcher         org.levimc.launcher                  W  ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.gu.apk' with 1 weak references
2025-07-31 00:44:40.784 27660-27667 levimc.launcher         org.levimc.launcher                  W  ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.hi.apk' with 1 weak references
2025-07-31 00:44:40.784 27660-27660 InsetsController        org.levimc.launcher                  I  setRequestedVisibleTypes: visible=false, mask=statusBars captionBar, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1498 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 android.view.InsetsController.hide:1452 android.view.InsetsController.hide:1368 android.view.ViewRootImpl.controlInsetsForCompatibility:3953 android.view.ViewRootImpl.setView:1955 android.view.WindowManagerGlobal.addView:578 android.view.WindowManagerImpl.addView:158 android.app.ActivityThread.handleResumeActivity:6064 
2025-07-31 00:44:40.784 27660-27667 levimc.launcher         org.levimc.launcher                  W  ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.kn.apk' with 1 weak references
2025-07-31 00:44:40.784 27660-27667 levimc.launcher         org.levimc.launcher                  W  ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.mr.apk' with 1 weak references
2025-07-31 00:44:40.784 27660-27667 levimc.launcher         org.levimc.launcher                  W  ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.ta.apk' with 1 weak references
2025-07-31 00:44:40.784 27660-27667 levimc.launcher         org.levimc.launcher                  W  ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.te.apk' with 1 weak references
2025-07-31 00:44:40.784 27660-27667 levimc.launcher         org.levimc.launcher                  W  ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_config.xxhdpi.apk' with 1 weak references
2025-07-31 00:44:40.784 27660-27667 levimc.launcher         org.levimc.launcher                  W  ApkAssets: Deleting an ApkAssets object '<empty> and /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/split_install_pack.apk' with 1 weak references
2025-07-31 00:44:40.797 27660-27660 InputTransport          org.levimc.launcher                  D  Input channel constructed: '99b103 ', fd=154
2025-07-31 00:44:40.798 27660-27660 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-31 00:44:40.799 27660-27660 VRI[Launcher]@947c63b   org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-31 00:44:40.800 27660-27660 VRI[Launcher]@947c63b   org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@725a396 IsHRR=false TM=true
2025-07-31 00:44:40.801 27660-27660 ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-31 00:44:40.801 27660-27660 ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-31 00:44:40.832 27660-27660 BufferQueueConsumer     org.levimc.launcher                  D  [](id:6c0c00000003,api:0,p:-1,c:27660) connect: controlledByApp=false
2025-07-31 00:44:40.832 27660-27660 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@947c63b#3](f:0,a:0,s:0) constructor()
2025-07-31 00:44:40.833 27660-27660 BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[Launcher]@947c63b mNativeObject= 0xb4000079881a1800 sc.mNativeObject= 0xb4000079e5a2e200 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-31 00:44:40.835 27660-27660 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[Launcher]@947c63b mNativeObject= 0xb4000079881a1800 sc.mNativeObject= 0xb4000079e5a2e200 format= 4 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-31 00:44:40.835 27660-27660 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@947c63b#3](f:0,a:0,s:0) update width=2340 height=1080 format=4 mTransformHint=4
2025-07-31 00:44:40.835 27660-27660 VRI[Launcher]@947c63b   org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=false req=(2340,1080)0 dur=29 res=0x3 s={true 0xb4000079884e8000} ch=true seqId=0
2025-07-31 00:44:40.836 27660-27660 VRI[Launcher]@947c63b   org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-31 00:44:40.837 27660-27660 VRI[Launcher]@947c63b   org.levimc.launcher                  I  ViewRootImpl >> surfaceCreated
2025-07-31 00:44:40.837 27660-27660 VRI[Launcher]@947c63b   org.levimc.launcher                  I  ViewRootImpl >> surfaceChanged W=2340, H=1080)
2025-07-31 00:44:40.837 27660-27660 VRI[Launcher]@947c63b   org.levimc.launcher                  D  applyTransactionOnDraw applyImmediately
2025-07-31 00:44:40.838 27660-27660 VRI[Launcher]@947c63b   org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-31 00:44:40.838 27660-27660 VRI[Launcher]@947c63b   org.levimc.launcher                  D  applyTransactionOnDraw applyImmediately
2025-07-31 00:44:40.839 27660-27660 VRI[Launcher]@947c63b   org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[Launcher]@947c63b#8
2025-07-31 00:44:40.839 27660-27660 VRI[Launcher]@947c63b   org.levimc.launcher                  I  Creating new active sync group VRI[Launcher]@947c63b#9
2025-07-31 00:44:40.840 27660-27660 VRI[Launcher]@947c63b   org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-31 00:44:40.848 27660-27660 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleInsetsControlChanged:2888, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-31 00:44:40.848 27660-27660 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=statusBars, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher
2025-07-31 00:44:40.849 27660-27660 InsetsController        org.levimc.launcher                  I  controlAnimationUncheckedInner: Added types=navigationBars, animType=0, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1502 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 
2025-07-31 00:44:40.856 27660-27660 VRI[Launcher]@947c63b   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-31 00:44:40.856 27660-27660 VRI[Launcher]@947c63b   org.levimc.launcher                  I  handleResized mSyncSeqId = 0
2025-07-31 00:44:40.856 27660-27660 VRI[Launcher]@947c63b   org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.handleResized:2864 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:13691 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-07-31 00:44:40.868 27660-27660 VRI[Launcher]@947c63b   org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[Launcher]@947c63b#10
2025-07-31 00:44:40.868 27660-27660 VRI[Launcher]@947c63b   org.levimc.launcher                  I  Creating new active sync group VRI[Launcher]@947c63b#11
2025-07-31 00:44:40.868 27660-27660 VRI[Launcher]@947c63b   org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-31 00:44:40.879 27660-27660 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-31 00:44:40.879 27660-27660 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-31 00:44:40.884 27660-27669 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=146
2025-07-31 00:44:40.898 27660-27660 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher
2025-07-31 00:44:41.136 27660-27660 InsetsController        org.levimc.launcher                  I  cancelAnimation: types=navigationBars, animType=0, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.notifyFinished:1890 android.view.InsetsAnimationThreadControlRunner$1.lambda$notifyFinished$0:87 android.view.InsetsAnimationThreadControlRunner$1.$r8$lambda$cDFF0h4Ncq-8EXdGszv69jrUu7c:0 
2025-07-31 00:44:41.229 27660-27660 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  handleAppVisibility mAppVisible = true visible = false
2025-07-31 00:44:41.229 27660-27660 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-31 00:44:41.242 27660-27660 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  Relayout returned: old=(982,335,1358,745) new=(982,335,1358,745) relayoutAsync=false req=(376,410)8 dur=6 res=0x2 s={false 0x0} ch=true seqId=0
2025-07-31 00:44:41.242 27660-27660 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  Not drawing due to not visible. Reason=!mAppVisible && !mForceDecorViewVisibility
2025-07-31 00:44:41.243 27660-27660 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  handleAppVisibility mAppVisible = true visible = false
2025-07-31 00:44:41.243 27660-27660 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-31 00:44:41.243 27660-27660 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  stopped(true) old = false
2025-07-31 00:44:41.243 27660-27660 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  D  WindowStopped on org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity set to true
2025-07-31 00:44:41.246 27660-27660 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  stopped(true) old = false
2025-07-31 00:44:41.246 27660-27660 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  WindowStopped on org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity set to true
2025-07-31 00:44:41.246 27660-27680 HWUI                    org.levimc.launcher                  D  CacheManager::trimMemory(20)
2025-07-31 00:44:41.251 27660-27660 WindowOnBackDispatcher  org.levimc.launcher                  W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda15@41d345e
2025-07-31 00:44:41.253 27660-27680 HWUI                    org.levimc.launcher                  D  CacheManager::trimMemory(20)
2025-07-31 00:44:41.253 27660-27660 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  dispatchDetachedFromWindow
2025-07-31 00:44:41.255 27660-27660 InputTransport          org.levimc.launcher                  D  Input channel destroyed: '27f03c8', fd=206
2025-07-31 00:44:41.256 27660-27671 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@5e3fe5b#2](f:0,a:3,s:0) destructor()
2025-07-31 00:44:41.256 27660-27671 BufferQueueConsumer     org.levimc.launcher                  D  [VRI[MainActivity]@5e3fe5b#2(BLAST Consumer)2](id:6c0c00000002,api:0,p:-1,c:27660) disconnect
2025-07-31 00:44:41.256 27660-27671 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@2f3909d#1](f:0,a:3,s:0) destructor()
2025-07-31 00:44:41.256 27660-27671 BufferQueueConsumer     org.levimc.launcher                  D  [VRI[MainActivity]@2f3909d#1(BLAST Consumer)1](id:6c0c00000001,api:0,p:-1,c:27660) disconnect
2025-07-31 00:44:41.259 27660-27660 WindowManager           org.levimc.launcher                  E  android.view.WindowLeaked: Activity org.levimc.launcher.ui.activities.MainActivity has leaked window com.android.internal.policy.DecorView{3ab896a V.ED..... R.....ID 0,0-376,410 aid=1073741824}[MainActivity] that was originally added here
                                                                                                    	at android.view.ViewRootImpl.<init>(ViewRootImpl.java:1527)
                                                                                                    	at android.view.ViewRootImpl.<init>(ViewRootImpl.java:1502)
                                                                                                    	at android.view.WindowManagerGlobal.addView(WindowManagerGlobal.java:544)
                                                                                                    	at android.view.WindowManagerImpl.addView(WindowManagerImpl.java:158)
                                                                                                    	at android.app.Dialog.show(Dialog.java:511)
                                                                                                    	at org.levimc.launcher.core.minecraft.MinecraftLauncher.showLoading(MinecraftLauncher.java:307)
                                                                                                    	at org.levimc.launcher.core.minecraft.MinecraftLauncher$$ExternalSyntheticLambda4.run(D8$$SyntheticClass:0)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:959)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:100)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:257)
                                                                                                    	at android.os.Looper.loop(Looper.java:342)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9638)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:619)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:929)
2025-07-31 00:44:41.259 27660-27660 WindowManager           org.levimc.launcher                  I  WindowManagerGlobal#removeView, ty=2, view=com.android.internal.policy.DecorView{3ab896a V.ED..... R.....ID 0,0-376,410 aid=1073741824}[MainActivity], caller=android.view.WindowManagerGlobal.closeAllExceptView:672 android.view.WindowManagerGlobal.closeAll:644 android.app.ActivityThread.handleDestroyActivity:6747 
2025-07-31 00:44:41.259 27660-27680 HWUI                    org.levimc.launcher                  D  CacheManager::trimMemory(20)
2025-07-31 00:44:41.261 27660-27660 WindowOnBackDispatcher  org.levimc.launcher                  W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda15@270d236
2025-07-31 00:44:41.261 27660-27660 VRI[MainAc...y]@5e3fe5b org.levimc.launcher                  I  dispatchDetachedFromWindow
2025-07-31 00:44:41.262 27660-27660 InputTransport          org.levimc.launcher                  D  Input channel destroyed: '3b5b1cf', fd=149
2025-07-31 00:44:41.269  1598-1804  WindowManager           system_server                        E  win=Window{3b5b1cf u0 org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity EXITING} destroySurfaces: appStopped=false cleanupOnResume=false win.mWindowRemovalAllowed=true win.mRemoveOnExit=true win.mViewVisibility=8 caller=com.android.server.wm.WindowState.onExitAnimationDone:222 com.android.server.wm.WindowState.onAnimationFinished:161 com.android.server.wm.WindowContainer$$ExternalSyntheticLambda5.onAnimationFinished:26 com.android.server.wm.SurfaceAnimator.cancelAnimation:19 com.android.server.wm.SurfaceAnimator.cancelAnimation:1 com.android.server.wm.WindowContainer.setParent:49 com.android.server.wm.WindowContainer.removeChild:17  