{
    find_program_fetch_package_xmake = {
        ninja = false,
        cmake = false
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_package_android_armeabi-v7a_fetch_package_xmake"] = {
        ["xmake::fmt_59a17d68dbe340a1a0c7e1c511c99e36_release_10.2.1_external"] = {
            static = true,
            version = "10.2.1",
            license = "MIT",
            libfiles = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib\libfmt.a]]
            },
            linkdirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib]]
            },
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\include]]
            },
            links = {
                "fmt"
            }
        },
        ["xmake::nlohmann_json_be0d1f3d98814d41bdf16f6957e7a2d6_release_v3.11.3_external"] = {
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6\include]]
            },
            version = "v3.11.3",
            license = "MIT"
        }
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    find_program_fetch_package_system = {
        cmake = false
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-mmsa"] = true,
            ["-mno-save-restore"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-D"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-mlong-calls"] = true,
            ["-mpacked-stack"] = true,
            ["-idirafter"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-ftime-trace"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-mno-outline"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-print-runtime-dir"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-C"] = true,
            ["-moutline-atomics"] = true,
            ["-dependency-dot"] = true,
            ["-fzvector"] = true,
            ["-mno-global-merge"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-mno-crc"] = true,
            ["-mfp64"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-ffixed-x1"] = true,
            ["-module-dependency-dir"] = true,
            ["-fno-trigraphs"] = true,
            ["-gdwarf-5"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-pedantic"] = true,
            ["-fcall-saved-x13"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-fobjc-arc"] = true,
            ["-mno-msa"] = true,
            ["-print-target-triple"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-mrelax"] = true,
            ["-print-multiarch"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-fno-declspec"] = true,
            ["-cl-mad-enable"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-fsanitize-stats"] = true,
            ["-fno-lto"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-static-libsan"] = true,
            ["-fmath-errno"] = true,
            ["-emit-ast"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-mcode-object-v3"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-gdwarf-3"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-mno-restrict-it"] = true,
            ["-fexceptions"] = true,
            ["-ffixed-x4"] = true,
            ["-ffixed-r9"] = true,
            ["-ffixed-d1"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-freroll-loops"] = true,
            ["-fno-use-init-array"] = true,
            ["-isystem"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-fcall-saved-x11"] = true,
            ["-fcall-saved-x14"] = true,
            ["-fwasm-exceptions"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-mstack-arg-probe"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-mno-embedded-data"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-finstrument-functions"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-gline-directives-only"] = true,
            ["-iwithprefixbefore"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-fno-jump-tables"] = true,
            ["-mno-cumode"] = true,
            ["-mthread-model"] = true,
            ["-mtgsplit"] = true,
            ["-fdigraphs"] = true,
            ["-mcmse"] = true,
            ["-P"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-fno-new-infallible"] = true,
            ["-fignore-exceptions"] = true,
            ["-fcxx-modules"] = true,
            ["-ffixed-x2"] = true,
            ["-fsanitize-trap"] = true,
            ["-o"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-F"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-fno-profile-generate"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-fgpu-rdc"] = true,
            ["-fmodules"] = true,
            ["-fseh-exceptions"] = true,
            ["-fkeep-static-consts"] = true,
            ["--config"] = true,
            ["-fdebug-types-section"] = true,
            ["-E"] = true,
            ["-msave-restore"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-fno-unroll-loops"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-ffixed-x5"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fjump-tables"] = true,
            ["-mibt-seal"] = true,
            ["-fcall-saved-x12"] = true,
            ["-nohipwrapperinc"] = true,
            ["-fno-spell-checking"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-Tdata"] = true,
            ["-fsave-optimization-record"] = true,
            ["-Xassembler"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-malign-double"] = true,
            ["-ffixed-d4"] = true,
            ["-relocatable-pch"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-mno-execute-only"] = true,
            ["-mno-outline-atomics"] = true,
            ["--help-hidden"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-ffixed-x15"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-fxray-instrument"] = true,
            ["-fno-stack-protector"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-fno-signed-zeros"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-arch"] = true,
            ["-fpascal-strings"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-fvectorize"] = true,
            ["-faddrsig"] = true,
            ["-pg"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-fmodules-search-all"] = true,
            ["-L"] = true,
            ["-mno-long-calls"] = true,
            ["-fno-signed-char"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-ffixed-x10"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-Xanalyzer"] = true,
            ["-fenable-matrix"] = true,
            ["-ffixed-x29"] = true,
            ["-mno-hvx"] = true,
            ["--hip-link"] = true,
            ["-fpch-codegen"] = true,
            ["-verify-pch"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-fopenmp"] = true,
            ["-ffixed-x22"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-ffixed-x25"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fforce-enable-int128"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-help"] = true,
            ["-fobjc-weak"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-fdata-sections"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-gdwarf"] = true,
            ["-mmark-bti-property"] = true,
            ["-fasync-exceptions"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["--no-cuda-version-check"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-mextern-sdata"] = true,
            ["-fshow-skipped-includes"] = true,
            ["--verify-debug-info"] = true,
            ["-fstack-protector-all"] = true,
            ["-ibuiltininc"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-fstack-clash-protection"] = true,
            ["-x"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-fgnu-keywords"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-fcall-saved-x18"] = true,
            ["-miamcu"] = true,
            ["-fwritable-strings"] = true,
            ["-gline-tables-only"] = true,
            ["-mwavefrontsize64"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-w"] = true,
            ["-fno-digraphs"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-mno-packets"] = true,
            ["-maix-struct-return"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-ffixed-x14"] = true,
            ["-fpcc-struct-return"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-M"] = true,
            ["-fapplication-extension"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-fgnu89-inline"] = true,
            ["-fno-short-wchar"] = true,
            ["-mstackrealign"] = true,
            ["-z"] = true,
            ["-fslp-vectorize"] = true,
            ["-mno-memops"] = true,
            ["-MD"] = true,
            ["-serialize-diagnostics"] = true,
            ["-mlong-double-80"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-gdwarf64"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-fsplit-stack"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-dsym-dir"] = true,
            ["-ffixed-a5"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-finline-hint-functions"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-ffixed-d7"] = true,
            ["-fstack-usage"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-ffixed-x17"] = true,
            ["-fno-builtin"] = true,
            ["-isystem-after"] = true,
            ["-print-resource-dir"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-fno-split-stack"] = true,
            ["-gembed-source"] = true,
            ["--analyzer-output"] = true,
            ["-gdwarf-4"] = true,
            ["-ffreestanding"] = true,
            ["-ffixed-x8"] = true,
            ["-pthread"] = true,
            ["-mhvx-qfloat"] = true,
            ["-mhvx"] = true,
            ["-ftrapv"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-mnvj"] = true,
            ["-T"] = true,
            ["-frwpi"] = true,
            ["-fgpu-sanitize"] = true,
            ["-ffixed-x6"] = true,
            ["-fintegrated-cc1"] = true,
            ["-mlong-double-64"] = true,
            ["-traditional-cpp"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-shared-libsan"] = true,
            ["-mfentry"] = true,
            ["-emit-module"] = true,
            ["-ffunction-sections"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-nogpuinc"] = true,
            ["-save-temps"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-index-header-map"] = true,
            ["-fcf-protection"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-flto"] = true,
            ["-mpackets"] = true,
            ["-fno-elide-type"] = true,
            ["-fuse-line-directives"] = true,
            ["-fopenmp-simd"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-fno-cxx-modules"] = true,
            ["-MF"] = true,
            ["-nostdinc"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-nobuiltininc"] = true,
            ["-time"] = true,
            ["-fcoverage-mapping"] = true,
            ["-Xopenmp-target"] = true,
            ["-fprofile-generate"] = true,
            ["-fshort-enums"] = true,
            ["-fconvergent-functions"] = true,
            ["-print-ivar-layout"] = true,
            ["-ffixed-x30"] = true,
            ["-mnvs"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-fno-addrsig"] = true,
            ["-fsized-deallocation"] = true,
            ["-trigraphs"] = true,
            ["-fno-discard-value-names"] = true,
            ["-fcxx-exceptions"] = true,
            ["-mbackchain"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-fstrict-enums"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-ffixed-x21"] = true,
            ["-fapple-kext"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-Xclang"] = true,
            ["-fcall-saved-x8"] = true,
            ["-fstack-size-section"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-ffixed-a2"] = true,
            ["-fno-sycl"] = true,
            ["-ffixed-x27"] = true,
            ["-mno-seses"] = true,
            ["-fms-extensions"] = true,
            ["-isysroot"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-MJ"] = true,
            ["-Wdeprecated"] = true,
            ["-fsycl"] = true,
            ["-mms-bitfields"] = true,
            ["-fshort-wchar"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-fintegrated-as"] = true,
            ["-mabicalls"] = true,
            ["-ffinite-loops"] = true,
            ["-fno-access-control"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fropi"] = true,
            ["-ffixed-a3"] = true,
            ["-ffixed-d3"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-fms-compatibility"] = true,
            ["-MMD"] = true,
            ["-fmerge-all-constants"] = true,
            ["-ffixed-x20"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-ffixed-x7"] = true,
            ["-foffload-lto"] = true,
            ["-fgnu-runtime"] = true,
            ["-rewrite-objc"] = true,
            ["-dependency-file"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-ffixed-point"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-fno-debug-macro"] = true,
            ["-fno-memory-profile"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-ffixed-x11"] = true,
            ["-U"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-fembed-bitcode"] = true,
            ["-ffixed-d0"] = true,
            ["--migrate"] = true,
            ["-static-openmp"] = true,
            ["-gcodeview"] = true,
            ["-emit-merged-ifs"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-MM"] = true,
            ["-fcoroutines-ts"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["--cuda-device-only"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-mno-abicalls"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-fmodules-ts"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-mno-movt"] = true,
            ["-MT"] = true,
            ["-ffixed-a6"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-extract-api"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-mcumode"] = true,
            ["-mfp32"] = true,
            ["-fmemory-profile"] = true,
            ["-meabi"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-mseses"] = true,
            ["-working-directory"] = true,
            ["-mmemops"] = true,
            ["-fno-rtti-data"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fminimize-whitespace"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-fno-autolink"] = true,
            ["-Tbss"] = true,
            ["--cuda-host-only"] = true,
            ["-I"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-dI"] = true,
            ["-mglobal-merge"] = true,
            ["-gdwarf-2"] = true,
            ["-emit-llvm"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-ffixed-x3"] = true,
            ["-fstack-protector"] = true,
            ["-fdiscard-value-names"] = true,
            ["-cl-no-stdinc"] = true,
            ["-pipe"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-imacros"] = true,
            ["-cl-finite-math-only"] = true,
            ["-fcall-saved-x9"] = true,
            ["-femulated-tls"] = true,
            ["-print-targets"] = true,
            ["-fdebug-macro"] = true,
            ["-mno-extern-sdata"] = true,
            ["-ffixed-d2"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-gcodeview-ghash"] = true,
            ["-ffixed-x28"] = true,
            ["-save-stats"] = true,
            ["-ffixed-x12"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-fopenmp-extensions"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-G"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-CC"] = true,
            ["-cl-opt-disable"] = true,
            ["-mrtd"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-I-"] = true,
            ["-mno-madd4"] = true,
            ["-fno-finite-loops"] = true,
            ["-mcrc"] = true,
            ["--version"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-mrecord-mcount"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-fno-strict-return"] = true,
            ["-mmadd4"] = true,
            ["-mno-neg-immediates"] = true,
            ["-Ttext"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-mlong-double-128"] = true,
            ["-ffixed-x18"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-fdeclspec"] = true,
            ["-gdwarf32"] = true,
            ["-fsystem-module"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-c"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-finline-functions"] = true,
            ["-fcs-profile-generate"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-nogpulib"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-mno-code-object-v3"] = true,
            ["-membedded-data"] = true,
            ["-faligned-allocation"] = true,
            ["-fno-offload-lto"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-fsigned-char"] = true,
            ["-gno-embed-source"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-undef"] = true,
            ["-print-effective-triple"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-mmt"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-mno-local-sdata"] = true,
            ["--precompile"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-emit-interface-stubs"] = true,
            ["-ffixed-a4"] = true,
            ["-moutline"] = true,
            ["-fcommon"] = true,
            ["-fglobal-isel"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-fno-show-column"] = true,
            ["-ffixed-x9"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-fprotect-parens"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-cxx-isystem"] = true,
            ["-ffixed-x16"] = true,
            ["-b"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-femit-all-decls"] = true,
            ["--analyze"] = true,
            ["-msoft-float"] = true,
            ["-fapprox-func"] = true,
            ["-mlocal-sdata"] = true,
            ["-dD"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-module-file-info"] = true,
            ["-mgpopt"] = true,
            ["-dM"] = true,
            ["-ffixed-x24"] = true,
            ["-funroll-loops"] = true,
            ["-mrestrict-it"] = true,
            ["-fno-pch-codegen"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-iprefix"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-iwithprefix"] = true,
            ["-mrelax-all"] = true,
            ["-fno-unique-section-names"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-ffixed-r19"] = true,
            ["-fno-exceptions"] = true,
            ["-rpath"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-mno-unaligned-access"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-ffixed-x19"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-print-supported-cpus"] = true,
            ["-mno-nvs"] = true,
            ["-fno-elide-constructors"] = true,
            ["-g"] = true,
            ["-fno-fixed-point"] = true,
            ["-fno-temp-file"] = true,
            ["-S"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fnew-infallible"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-ffixed-x23"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-MQ"] = true,
            ["-freg-struct-return"] = true,
            ["--emit-static-lib"] = true,
            ["-fno-operator-names"] = true,
            ["-ffixed-x26"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-mno-relax"] = true,
            ["-mlvi-hardening"] = true,
            ["-Qunused-arguments"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-mno-tgsplit"] = true,
            ["-fstandalone-debug"] = true,
            ["-MV"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-fxray-link-deps"] = true,
            ["--gpu-bundle-output"] = true,
            ["-munaligned-access"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-Xlinker"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-Xpreprocessor"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-Qn"] = true,
            ["-fansi-escape-codes"] = true,
            ["-mnop-mcount"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-mllvm"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-B"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-mno-implicit-float"] = true,
            ["-gmodules"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fpch-debuginfo"] = true,
            ["-fno-common"] = true,
            ["-fno-global-isel"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-include"] = true,
            ["-iquote"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-fno-rtti"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-MP"] = true,
            ["-mqdsp6-compat"] = true,
            ["-mexecute-only"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-mno-nvj"] = true,
            ["-v"] = true,
            ["-Qy"] = true,
            ["-ffixed-d6"] = true,
            ["-mskip-rax-setup"] = true,
            ["-fms-hotpatch"] = true,
            ["-ffast-math"] = true,
            ["-mnocrc"] = true,
            ["-freciprocal-math"] = true,
            ["-fno-show-source-location"] = true,
            ["-ftrigraphs"] = true,
            ["-fno-integrated-as"] = true,
            ["-mlvi-cfi"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fverbose-asm"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-ffixed-a1"] = true,
            ["-ffixed-x13"] = true,
            ["-fborland-extensions"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-ffixed-x31"] = true,
            ["-ffixed-d5"] = true,
            ["-fno-plt"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-ffixed-a0"] = true,
            ["-mno-gpopt"] = true,
            ["-include-pch"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-msvr4-struct-return"] = true,
            ["-MG"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-fobjc-exceptions"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-H"] = true,
            ["-fblocks"] = true,
            ["-iwithsysroot"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-fno-standalone-debug"] = true,
            ["-fmodules-decluse"] = true,
            ["-ivfsoverlay"] = true,
            ["-mno-mt"] = true,
            ["-print-search-dirs"] = true
        }
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            ndkver = 25,
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            sdkver = "21",
            cross = "arm-linux-androideabi-",
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]]
        }
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fvisibility-inlines-hidden"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    find_program = {
        git = [[C:\Program Files\Git\cmd\git.exe]],
        tar = [[C:\Windows\System32\tar.exe]],
        nim = false,
        clang = false,
        ["vswhere.exe"] = [[C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe]],
        zig = false,
        gzip = [[C:\msys64\usr\bin\gzip.exe]],
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-o"] = true,
            ["-dy"] = true,
            ["-dn"] = true,
            ["-m"] = true,
            ["--help"] = true,
            ["--no-gc-sections"] = true,
            ["--no-seh"] = true,
            ["--insert-timestamp"] = true,
            ["--nxcompat"] = true,
            ["--large-address-aware"] = true,
            ["--no-dynamicbase"] = true,
            ["--allow-multiple-definition"] = true,
            ["--enable-auto-import"] = true,
            ["-static"] = true,
            ["--exclude-all-symbols"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--strip-debug"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--dynamicbase"] = true,
            ["--gc-sections"] = true,
            ["-S"] = true,
            ["--fatal-warnings"] = true,
            ["--no-whole-archive"] = true,
            ["--disable-no-seh"] = true,
            ["-l"] = true,
            ["--Bstatic"] = true,
            ["--export-all-symbols"] = true,
            ["--no-demangle"] = true,
            ["--no-insert-timestamp"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--appcontainer"] = true,
            ["--version"] = true,
            ["--high-entropy-va"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--shared"] = true,
            ["--verbose"] = true,
            ["--whole-archive"] = true,
            ["--kill-at"] = true,
            ["-v"] = true,
            ["--tsaware"] = true,
            ["--Bdynamic"] = true,
            ["--strip-all"] = true,
            ["--disable-nxcompat"] = true,
            ["--disable-tsaware"] = true,
            ["--no-fatal-warnings"] = true,
            ["--disable-auto-import"] = true,
            ["--demangle"] = true,
            ["-s"] = true,
            ["-L"] = true,
            ["--disable-dynamicbase"] = true
        }
    }
}