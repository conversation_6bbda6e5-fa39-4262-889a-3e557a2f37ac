{
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--no-seh"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--strip-debug"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["-o"] = true,
            ["--enable-auto-import"] = true,
            ["--no-insert-timestamp"] = true,
            ["--disable-high-entropy-va"] = true,
            ["-v"] = true,
            ["--Bstatic"] = true,
            ["--allow-multiple-definition"] = true,
            ["-static"] = true,
            ["--whole-archive"] = true,
            ["--disable-no-seh"] = true,
            ["-m"] = true,
            ["--no-whole-archive"] = true,
            ["--tsaware"] = true,
            ["--kill-at"] = true,
            ["--demangle"] = true,
            ["-S"] = true,
            ["--gc-sections"] = true,
            ["--no-gc-sections"] = true,
            ["--strip-all"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--fatal-warnings"] = true,
            ["--version"] = true,
            ["-dy"] = true,
            ["-L"] = true,
            ["--shared"] = true,
            ["--exclude-all-symbols"] = true,
            ["--help"] = true,
            ["--no-demangle"] = true,
            ["--export-all-symbols"] = true,
            ["--large-address-aware"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["-l"] = true,
            ["--verbose"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--Bdynamic"] = true,
            ["--nxcompat"] = true,
            ["--disable-dynamicbase"] = true,
            ["--dynamicbase"] = true,
            ["--disable-nxcompat"] = true,
            ["--no-dynamicbase"] = true,
            ["--high-entropy-va"] = true,
            ["-dn"] = true,
            ["--no-fatal-warnings"] = true,
            ["--appcontainer"] = true,
            ["--insert-timestamp"] = true,
            ["--disable-auto-import"] = true,
            ["--disable-tsaware"] = true,
            ["-s"] = true
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_program_fetch_package_system = {
        cmake = false
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    find_program_fetch_package_xmake = {
        ninja = false,
        cmake = false
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_utils.binary.deplibs"] = {
        objdump = [[C:\msys64\usr\bin\objdump.exe]],
        ["llvm-objdump"] = false
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fvisibility-inlines-hidden"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-fconvergent-functions"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-gcodeview"] = true,
            ["-mbackchain"] = true,
            ["-meabi"] = true,
            ["-fno-digraphs"] = true,
            ["-fsycl"] = true,
            ["-cl-finite-math-only"] = true,
            ["-mno-outline"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-fprotect-parens"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-iwithsysroot"] = true,
            ["-gline-directives-only"] = true,
            ["-fno-integrated-as"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-cl-mad-enable"] = true,
            ["-fignore-exceptions"] = true,
            ["-fno-offload-lto"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-pg"] = true,
            ["-fno-show-source-location"] = true,
            ["-fno-pch-codegen"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-mrestrict-it"] = true,
            ["-print-target-triple"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["--cuda-host-only"] = true,
            ["-fforce-enable-int128"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-gno-embed-source"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["--emit-static-lib"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-fno-split-stack"] = true,
            ["-L"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-mlong-double-128"] = true,
            ["-mamdgpu-ieee"] = true,
            ["--gpu-bundle-output"] = true,
            ["-mno-code-object-v3"] = true,
            ["-Wdeprecated"] = true,
            ["-fcoverage-mapping"] = true,
            ["-freroll-loops"] = true,
            ["-mnop-mcount"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-moutline"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-mmsa"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-Xlinker"] = true,
            ["-ffixed-x4"] = true,
            ["-M"] = true,
            ["-help"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-fno-memory-profile"] = true,
            ["-index-header-map"] = true,
            ["-fopenmp-simd"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-mgpopt"] = true,
            ["--precompile"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-ffixed-x7"] = true,
            ["-imacros"] = true,
            ["-fapplication-extension"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-ffixed-x18"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-mno-msa"] = true,
            ["-fansi-escape-codes"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fropi"] = true,
            ["-fno-global-isel"] = true,
            ["-mwavefrontsize64"] = true,
            ["-mhvx"] = true,
            ["-mlvi-cfi"] = true,
            ["-ffixed-d5"] = true,
            ["-ffixed-a2"] = true,
            ["-fintegrated-as"] = true,
            ["-ftrigraphs"] = true,
            ["-fslp-vectorize"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-mno-madd4"] = true,
            ["--migrate"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-undef"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-fno-operator-names"] = true,
            ["-fno-addrsig"] = true,
            ["-Xopenmp-target"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-mno-movt"] = true,
            ["-CC"] = true,
            ["-ffixed-d7"] = true,
            ["-gdwarf-3"] = true,
            ["-mrecord-mcount"] = true,
            ["-mnocrc"] = true,
            ["-fembed-bitcode"] = true,
            ["-dM"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-fmath-errno"] = true,
            ["-ffixed-x23"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-fapple-kext"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-mskip-rax-setup"] = true,
            ["-mno-cumode"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-ffixed-r9"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-fgpu-sanitize"] = true,
            ["-fpch-debuginfo"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-c"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-fenable-matrix"] = true,
            ["-fpascal-strings"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-fno-unroll-loops"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-ffinite-loops"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fcommon"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-B"] = true,
            ["-emit-merged-ifs"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-b"] = true,
            ["-mexecute-only"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-mglobal-merge"] = true,
            ["-mcode-object-v3"] = true,
            ["-MQ"] = true,
            ["-verify-pch"] = true,
            ["-ffixed-x27"] = true,
            ["-mrelax"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-mno-packets"] = true,
            ["-ffast-math"] = true,
            ["-C"] = true,
            ["-mrelax-all"] = true,
            ["-fsanitize-trap"] = true,
            ["-MV"] = true,
            ["-ffixed-x31"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-iquote"] = true,
            ["-fstack-clash-protection"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-MJ"] = true,
            ["-fobjc-exceptions"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-D"] = true,
            ["-fno-new-infallible"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-print-supported-cpus"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-fno-declspec"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-ffixed-a6"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-fobjc-arc"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-g"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-fstandalone-debug"] = true,
            ["-fms-extensions"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-ffixed-x20"] = true,
            ["-fno-discard-value-names"] = true,
            ["-Xassembler"] = true,
            ["-gembed-source"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fno-common"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-Ttext"] = true,
            ["-mno-save-restore"] = true,
            ["-fstack-protector-all"] = true,
            ["-F"] = true,
            ["-relocatable-pch"] = true,
            ["-fms-hotpatch"] = true,
            ["-MP"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-MMD"] = true,
            ["-rpath"] = true,
            ["-dependency-file"] = true,
            ["-fno-sycl"] = true,
            ["-mtgsplit"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-fverbose-asm"] = true,
            ["-ffixed-a3"] = true,
            ["-mno-mt"] = true,
            ["-print-multiarch"] = true,
            ["--config"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fno-autolink"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-msvr4-struct-return"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-fcall-saved-x18"] = true,
            ["-nohipwrapperinc"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-mcrc"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-fmemory-profile"] = true,
            ["-save-stats"] = true,
            ["-H"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-ffixed-a1"] = true,
            ["-mpackets"] = true,
            ["-ffixed-a5"] = true,
            ["-fxray-link-deps"] = true,
            ["-mno-long-calls"] = true,
            ["-mlong-calls"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-mnvs"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-fopenmp"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fobjc-weak"] = true,
            ["-fwritable-strings"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-z"] = true,
            ["-ffixed-x10"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["--no-cuda-version-check"] = true,
            ["-fcall-saved-x14"] = true,
            ["-I-"] = true,
            ["--analyze"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-mms-bitfields"] = true,
            ["-fjump-tables"] = true,
            ["-mabicalls"] = true,
            ["-fstack-protector"] = true,
            ["-ffixed-x30"] = true,
            ["-fmodules"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-mnvj"] = true,
            ["-fno-temp-file"] = true,
            ["-fms-compatibility"] = true,
            ["-mstack-arg-probe"] = true,
            ["-mextern-sdata"] = true,
            ["-cxx-isystem"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-Tbss"] = true,
            ["-Xanalyzer"] = true,
            ["-x"] = true,
            ["-femulated-tls"] = true,
            ["-fborland-extensions"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-fzvector"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-fprofile-generate"] = true,
            ["-fuse-line-directives"] = true,
            ["-ffixed-d3"] = true,
            ["-gdwarf-4"] = true,
            ["-fno-xray-function-index"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-gline-tables-only"] = true,
            ["-finline-hint-functions"] = true,
            ["-fshort-wchar"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-print-resource-dir"] = true,
            ["-fdeclspec"] = true,
            ["-ffixed-a4"] = true,
            ["-ffixed-x29"] = true,
            ["-fstack-usage"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-ffixed-x8"] = true,
            ["-iwithprefixbefore"] = true,
            ["-mno-local-sdata"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-fcxx-modules"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-mno-nvj"] = true,
            ["-serialize-diagnostics"] = true,
            ["-mlvi-hardening"] = true,
            ["-emit-module"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-fno-stack-protector"] = true,
            ["-nobuiltininc"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-ffixed-d4"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-fno-lto"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-idirafter"] = true,
            ["-mno-memops"] = true,
            ["-mno-outline-atomics"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-fno-short-wchar"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-finstrument-functions"] = true,
            ["--help-hidden"] = true,
            ["-pipe"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-mllvm"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-fwasm-exceptions"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-fno-elide-type"] = true,
            ["-traditional-cpp"] = true,
            ["-time"] = true,
            ["-cl-no-stdinc"] = true,
            ["-finline-functions"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fsplit-stack"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-iprefix"] = true,
            ["-mno-neg-immediates"] = true,
            ["-ftime-trace"] = true,
            ["-Qn"] = true,
            ["-fmodules-ts"] = true,
            ["-ffixed-x25"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-emit-llvm"] = true,
            ["-nogpulib"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-fsjlj-exceptions"] = true,
            ["--verify-debug-info"] = true,
            ["-gdwarf"] = true,
            ["-ffixed-x1"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-fdigraphs"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fcall-saved-x11"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-ffixed-x5"] = true,
            ["-fno-spell-checking"] = true,
            ["-fsanitize-stats"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-pedantic"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-Xclang"] = true,
            ["-fgnu-runtime"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-iwithprefix"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-dD"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-mthread-model"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-fgpu-rdc"] = true,
            ["-ffixed-point"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-fintegrated-cc1"] = true,
            ["-ffixed-x22"] = true,
            ["-working-directory"] = true,
            ["-fmodules-decluse"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-ivfsoverlay"] = true,
            ["-fno-plt"] = true,
            ["-S"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-mfentry"] = true,
            ["-mno-implicit-float"] = true,
            ["-ffixed-x21"] = true,
            ["-fdebug-types-section"] = true,
            ["-ffixed-x6"] = true,
            ["-ffixed-x28"] = true,
            ["-fdiscard-value-names"] = true,
            ["-mfp64"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-mlong-double-64"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-fcxx-exceptions"] = true,
            ["-nostdinc"] = true,
            ["-freg-struct-return"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-print-effective-triple"] = true,
            ["-miamcu"] = true,
            ["-msoft-float"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-fcall-saved-x9"] = true,
            ["-fsigned-char"] = true,
            ["-gdwarf-2"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-fno-signed-zeros"] = true,
            ["-ffixed-x15"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-maix-struct-return"] = true,
            ["-fgnu89-inline"] = true,
            ["-mcmse"] = true,
            ["-fno-strict-return"] = true,
            ["-mmark-bti-property"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-fshort-enums"] = true,
            ["-fvectorize"] = true,
            ["-isystem-after"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-ffixed-x17"] = true,
            ["-mno-execute-only"] = true,
            ["-Tdata"] = true,
            ["-MT"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-rewrite-objc"] = true,
            ["-fsystem-module"] = true,
            ["-Xpreprocessor"] = true,
            ["-ffixed-x26"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-isysroot"] = true,
            ["-mmemops"] = true,
            ["-MD"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-I"] = true,
            ["-fexceptions"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-mlocal-sdata"] = true,
            ["-fno-profile-generate"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-fnew-infallible"] = true,
            ["-fno-use-init-array"] = true,
            ["-module-dependency-dir"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-fdebug-macro"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-emit-interface-stubs"] = true,
            ["-faddrsig"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-fno-standalone-debug"] = true,
            ["-mno-embedded-data"] = true,
            ["-mpacked-stack"] = true,
            ["-fcall-saved-x13"] = true,
            ["-ftrapv"] = true,
            ["-mmt"] = true,
            ["-gdwarf64"] = true,
            ["-print-ivar-layout"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-fcoroutines-ts"] = true,
            ["-gdwarf-5"] = true,
            ["-mhvx-qfloat"] = true,
            ["-fno-rtti"] = true,
            ["-mfp32"] = true,
            ["-mno-global-merge"] = true,
            ["-save-temps"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-ffixed-x19"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-ffixed-x11"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-gdwarf32"] = true,
            ["-fsave-optimization-record"] = true,
            ["-mlong-double-80"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-fno-rtti-data"] = true,
            ["-ffixed-d6"] = true,
            ["-static-openmp"] = true,
            ["-arch"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-malign-double"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-trigraphs"] = true,
            ["-fstrict-enums"] = true,
            ["-fsized-deallocation"] = true,
            ["-dsym-dir"] = true,
            ["-fgnu-keywords"] = true,
            ["-mibt-seal"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-emit-ast"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-fno-cxx-modules"] = true,
            ["-mno-gpopt"] = true,
            ["-mno-extern-sdata"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-print-search-dirs"] = true,
            ["-fno-unique-section-names"] = true,
            ["-pthread"] = true,
            ["-mrtd"] = true,
            ["-fasync-exceptions"] = true,
            ["-fno-debug-macro"] = true,
            ["--version"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-fno-jump-tables"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-fno-finite-loops"] = true,
            ["-fno-elide-constructors"] = true,
            ["-femit-all-decls"] = true,
            ["-G"] = true,
            ["-mseses"] = true,
            ["-ffunction-sections"] = true,
            ["-fcall-saved-x12"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-fcall-saved-x10"] = true,
            ["-module-file-info"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["--analyzer-output"] = true,
            ["-freciprocal-math"] = true,
            ["-membedded-data"] = true,
            ["-fno-fixed-point"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-fglobal-isel"] = true,
            ["-nogpuinc"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-ffixed-d0"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-isystem"] = true,
            ["--cuda-device-only"] = true,
            ["-MM"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fcall-saved-x8"] = true,
            ["-v"] = true,
            ["-mno-tgsplit"] = true,
            ["-print-runtime-dir"] = true,
            ["-flto"] = true,
            ["-ffixed-x2"] = true,
            ["--hip-link"] = true,
            ["-include"] = true,
            ["-gcodeview-ghash"] = true,
            ["-ffixed-d1"] = true,
            ["-mno-crc"] = true,
            ["-ibuiltininc"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-mcumode"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-foffload-lto"] = true,
            ["-MF"] = true,
            ["-mstackrealign"] = true,
            ["-munaligned-access"] = true,
            ["-Qunused-arguments"] = true,
            ["-fno-trigraphs"] = true,
            ["-fseh-exceptions"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-ffixed-a0"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-fno-signed-char"] = true,
            ["-ffixed-x12"] = true,
            ["-faligned-allocation"] = true,
            ["-mno-restrict-it"] = true,
            ["-fmodules-search-all"] = true,
            ["-fno-show-column"] = true,
            ["-w"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-mno-relax"] = true,
            ["-ffixed-x3"] = true,
            ["-gmodules"] = true,
            ["-funroll-loops"] = true,
            ["-mno-hvx"] = true,
            ["-print-targets"] = true,
            ["-shared-libsan"] = true,
            ["-ffixed-x24"] = true,
            ["-Qy"] = true,
            ["-msave-restore"] = true,
            ["-o"] = true,
            ["-fapprox-func"] = true,
            ["-E"] = true,
            ["-ffixed-x14"] = true,
            ["-fno-access-control"] = true,
            ["-ffreestanding"] = true,
            ["-cl-opt-disable"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-fpcc-struct-return"] = true,
            ["-mno-abicalls"] = true,
            ["-fminimize-whitespace"] = true,
            ["-ffixed-d2"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-mqdsp6-compat"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-fpch-codegen"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-mno-seses"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-dI"] = true,
            ["-U"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-extract-api"] = true,
            ["-fno-exceptions"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-fmerge-all-constants"] = true,
            ["-frwpi"] = true,
            ["-fxray-instrument"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-static-libsan"] = true,
            ["-fdata-sections"] = true,
            ["-include-pch"] = true,
            ["-P"] = true,
            ["-mmadd4"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-dependency-dot"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-MG"] = true,
            ["-mno-nvs"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-moutline-atomics"] = true,
            ["-ffixed-x16"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-fblocks"] = true,
            ["-fcf-protection"] = true,
            ["-mno-unaligned-access"] = true,
            ["-ffixed-x9"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-ffixed-r19"] = true,
            ["-fcs-profile-generate"] = true,
            ["-T"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fstack-size-section"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fno-builtin"] = true,
            ["-ffixed-x13"] = true,
            ["-mhvx-ieee-fp"] = true
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_program = {
        nim = false,
        tar = [[C:\Windows\System32\tar.exe]],
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        zig = false,
        git = [[C:\Program Files\Git\cmd\git.exe]],
        ["vswhere.exe"] = [[C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe]],
        clang = false,
        gzip = [[C:\msys64\usr\bin\gzip.exe]]
    },
    ["find_package_android_armeabi-v7a_fetch_package_xmake"] = {
        ["xmake::nlohmann_json_be0d1f3d98814d41bdf16f6957e7a2d6_release_v3.11.3_external"] = {
            license = "MIT",
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6\include]]
            },
            version = "v3.11.3"
        },
        ["xmake::fmt_59a17d68dbe340a1a0c7e1c511c99e36_release_10.2.1_external"] = {
            license = "MIT",
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\include]]
            },
            links = {
                "fmt"
            },
            libfiles = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib\libfmt.a]]
            },
            static = true,
            version = "10.2.1",
            linkdirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib]]
            }
        }
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            cross = "arm-linux-androideabi-",
            ndkver = 25,
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            sdkver = "21",
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]]
        }
    }
}